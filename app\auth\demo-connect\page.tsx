'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import jwt from 'jsonwebtoken';
import { mockZerodhaAuth } from '@/app/config/demoMode';

interface InvitationData {
  masterEmail: string;
  childEmail: string;
  masterId: string;
  type: string;
}

export default function DemoConnectPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isProcessing, setIsProcessing] = useState(true);
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    const processInvitation = async () => {
      try {
        const token = searchParams.get('token');

        if (!token) {
          setError('Invalid invitation link. Token is missing.');
          setStatus('error');
          setIsProcessing(false);
          return;
        }

        // Decode the JWT token to get invitation data (without verification on client side)
        let invitationData: InvitationData;

        try {
          invitationData = jwt.decode(token) as InvitationData;

          if (!invitationData || invitationData.type !== 'invitation') {
            throw new Error('Invalid token format');
          }
        } catch (err) {
          setError('Invalid invitation token. Please request a new invitation.');
          setStatus('error');
          setIsProcessing(false);
          return;
        }

        // Mock Zerodha authentication for demo mode
        const mockAuthData = await mockZerodhaAuth(invitationData.childEmail);

        // Create child user data
        const childUserData = {
          email: invitationData.childEmail,
          name: invitationData.childEmail.split('@')[0].replace('.', ' ').toUpperCase(),
          role: 'child' as const,
          zerodhaAccessToken: mockAuthData.access_token,
          zerodhaRefreshToken: mockAuthData.refresh_token,
          zerodhaUserId: mockAuthData.user_id,
          masterId: invitationData.masterId,
          masterEmail: invitationData.masterEmail,
          invitationToken: token
        };

        // Store demo user data in localStorage for demo mode
        localStorage.setItem('demo_child_user', JSON.stringify(childUserData));
        localStorage.setItem('zerodha_access_token', mockAuthData.access_token);
        localStorage.setItem('zerodha_refresh_token', mockAuthData.refresh_token);
        localStorage.setItem('zerodha_user_id', mockAuthData.user_id);
        localStorage.setItem('user_email', invitationData.childEmail);
        localStorage.setItem('user_role', 'child');

        // Call API to create the master-child relationship in database
        const response = await fetch('/api/child/accept-invitation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            token: token,
            childUserData: childUserData
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to accept invitation');
        }

        const result = await response.json();

        setMessage(`Successfully connected to ${invitationData.masterEmail}'s trading account in demo mode!`);
        setStatus('success');

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          router.push('/dashboard');
        }, 2000);

      } catch (err) {
        console.error('Demo connection error:', err);
        setError(err instanceof Error ? err.message : 'Failed to process invitation');
        setStatus('error');
      } finally {
        setIsProcessing(false);
      }
    };

    processInvitation();
  }, [searchParams, router]);

  const handleRetry = () => {
    setIsProcessing(true);
    setStatus('processing');
    setError('');
    setMessage('');
    // Trigger the effect again
    window.location.reload();
  };

  const handleGoHome = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Demo Connection
          </h1>
          <p className="text-gray-600">
            Processing your invitation to join CopyTrade
          </p>
        </div>

        <div className="bg-white shadow-lg rounded-lg p-6">
          {status === 'processing' && (
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600">Connecting your demo account...</p>
            </div>
          )}

          {status === 'success' && (
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-green-800 mb-2">Connection Successful!</h3>
              <p className="text-green-600 mb-4">{message}</p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <p className="text-blue-800 text-sm">
                  <strong>🚀 Demo Mode:</strong> You're now connected in demo mode. All trades will be simulated and no real money is involved.
                </p>
              </div>
              <p className="text-gray-600 text-sm">Redirecting to dashboard...</p>
            </div>
          )}

          {status === 'error' && (
            <div className="text-center">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-red-800 mb-2">Connection Failed</h3>
              <p className="text-red-600 mb-4">{error}</p>
              <div className="space-y-2">
                <button
                  onClick={handleRetry}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Try Again
                </button>
                <button
                  onClick={handleGoHome}
                  className="w-full bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
                >
                  Go to Home
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            This is a demo environment. No real trading or money is involved.
          </p>
        </div>
      </div>
    </div>
  );
}
